import { Component, OnInit, ElementRef, ViewChild, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import * as THREE from 'three';
import { AbstractThing, BoxThing, RectangleThing, SphereThing, TriangleThing } from './things';

export interface ThingData {
  type: string;
  name: string;
  status: 'on' | 'off' | string;
  pos: [number, number, number];
  width?: number;
  height?: number;
  depth?: number;
  radius?: number;
  base?: number;
  widthSegments?: number;
  heightSegments?: number;
}

@Component({
  selector: 'app-three',
  standalone: true,
  templateUrl: './three.component.html',
  styleUrls: ['./three.component.scss']
})
export class ThreeComponent implements OnInit, OnDestroy {
  @ViewChild('rendererContainer', { static: true }) rendererContainer!: ElementRef;

  private scene!: THREE.Scene;
  private camera!: THREE.OrthographicCamera;
  private renderer!: THREE.WebGLRenderer;
  private animationId!: number;
  private clock = new THREE.Clock();

  private robotPivot!: THREE.Object3D; // spostamento corpo
  private headPivot!: THREE.Object3D;  // rotazione testa (mouse)

  private movementState = {
    forward: false,
    backward: false,
    left: false,
    right: false,
    up: false,
    down: false,
  };

  // per mouse look
  private yaw = 0;   // rotazione orizzontale testa target
  private pitch = 0; // rotazione verticale testa target
  private readonly pitchLimit = Math.PI / 3; // ±60°
  private readonly yawSpeed = 0.0025;
  private readonly pitchSpeed = 0.0025;

  // smoothing facoltativo (disabilitato)
  private currentYaw = 0;
  private currentPitch = 0;
  private readonly smoothingEnabled = false;
  private readonly smoothingFactor = 0.1;

  // drag-based look state
  private isDragging = false;
  private prevMouse: { x: number; y: number } | null = null;

  // raycast point-and-click
  private raycaster = new THREE.Raycaster();
  private pointer = new THREE.Vector2();
  private selectedThingMesh: THREE.Object3D | null = null;
  private highlightOutline: THREE.LineSegments | null = null;
  private selectedObjectPosition: THREE.Vector3 | null = null;

  // camera animation for smooth transitions
  private isAnimatingCamera = false;
  private animationStartTime = 0;
  private animationDuration = 1000; // 1 second
  private startCameraPosition = new THREE.Vector3();
  private targetCameraPosition = new THREE.Vector3();
  private startCameraRotation = new THREE.Euler();
  private targetCameraRotation = new THREE.Euler();

  // scena di esempio
  private jsonScene: ThingData[] = [
    {
      type: 'box',
      name: 'Rack-1-Server-A',
      status: 'on',
      pos: [0, 0.5, 0],
      width: 1,
      height: 1,
      depth: 1
    },
    {
      type: 'sphere',
      name: 'Rack-1-Server-B',
      status: 'off',
      pos: [4, 0.5, 0],
      radius: 0.5,
      widthSegments: 16,
      heightSegments: 12
    },
    {
      type: 'triangle',
      name: 'Rack-2-Server-A',
      status: 'on',
      pos: [0, 0.5, -2],
      base: 1.5,
      height: 1
    },
    {
      type: 'rectangle',
      name: 'Rack-2-Server-B',
      status: 'on',
      pos: [4, 0.5, -2],
      width: 2,
      height: 1,
      depth: 0.2
    }
  ];

  ngOnInit() {
    this.initThree();
    this.loadJsonScene({ things: this.jsonScene });
    this.animate();
  }

  ngOnDestroy() {
    cancelAnimationFrame(this.animationId);
    this.renderer.dispose();

    // rimozione listener
    window.removeEventListener('keydown', this.onKeyDown);
    window.removeEventListener('keyup', this.onKeyUp);
    window.removeEventListener('resize', this.onResize);
    this.renderer.domElement.removeEventListener('mousedown', this.onMouseDown);
    this.renderer.domElement.removeEventListener('mouseup', this.onMouseUp);
    this.renderer.domElement.removeEventListener('mousemove', this.onMouseMove);
    this.renderer.domElement.removeEventListener('click', this.onClick);
    this.renderer.domElement.removeEventListener('dragstart', this.preventDrag);
  }

  private initThree() {
    this.scene = new THREE.Scene();
    this.scene.background = new THREE.Color(0x1a1a1a); // Dark background to match the mood

    // Renderer
    this.renderer = new THREE.WebGLRenderer({ antialias: true });
    this.renderer.setSize(
      this.rendererContainer.nativeElement.clientWidth,
      this.rendererContainer.nativeElement.clientHeight
    );
    this.renderer.shadowMap.enabled = true;
    this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    this.renderer.toneMapping = THREE.ACESFilmicToneMapping;
    this.renderer.toneMappingExposure = 1.2;
    this.rendererContainer.nativeElement.appendChild(this.renderer.domElement);

    // Camera & pivots - Isometric setup
    const aspect = this.rendererContainer.nativeElement.clientWidth /
      this.rendererContainer.nativeElement.clientHeight;
    const frustumSize = 20;
    this.camera = new THREE.OrthographicCamera(
      (frustumSize * aspect) / -2,
      (frustumSize * aspect) / 2,
      frustumSize / 2,
      frustumSize / -2,
      0.1,
      1000
    );

    this.robotPivot = new THREE.Object3D();
    this.scene.add(this.robotPivot);

    this.headPivot = new THREE.Object3D();
    this.robotPivot.add(this.headPivot);

    // Posizionamento camera per vista isometrica - looking at origin (0,0,0)
    this.camera.position.set(10, 10, 10);
    this.headPivot.add(this.camera);

    // Position robot pivot at origin and make camera look at it
    this.robotPivot.position.set(0, 0, 0);
    this.camera.lookAt(0, 0, 0);

    // Set initial camera angle for isometric view
    this.pitch = -Math.PI / 4; // -45 degrees looking down
    this.yaw = Math.PI / 4; // 45 degrees rotation for isometric angle
    this.currentPitch = this.pitch;
    this.currentYaw = this.yaw;

    // Luci
    const ambientLight = new THREE.AmbientLight(0x404040, 0.3); // Darker ambient for mood
    this.scene.add(ambientLight);
    const dirLight = new THREE.DirectionalLight(0xffffff, 0.8);
    dirLight.position.set(5, 10, 7.5);
    dirLight.castShadow = true;
    dirLight.shadow.mapSize.width = 2048;
    dirLight.shadow.mapSize.height = 2048;
    this.scene.add(dirLight);

    // Add neon green accent light
    const neonLight = new THREE.PointLight(0x00ff00, 10, 50);
    neonLight.position.set(0, 5  , 1);
    this.scene.add(neonLight);

    // Ground plane - glossy dark with reflections
    const groundGeometry = new THREE.PlaneGeometry(20, 20);
    const groundMaterial = new THREE.MeshPhysicalMaterial({
      color: 0x111111, // Very dark
      metalness: 0.1,
      roughness: 0.05, // Very smooth for glossy effect
      reflectivity: 0.9,
      clearcoat: 1.0,
      clearcoatRoughness: 0.1,
      transparent: false
    });
    const ground = new THREE.Mesh(groundGeometry, groundMaterial);
    ground.rotation.x = -Math.PI / 2; // Rotate to be horizontal
    ground.position.y = -0.01; // Slightly below y=0 to avoid z-fighting with grid
    ground.receiveShadow = true;
    this.scene.add(ground);

    // Add neon green acid pools
    // this.addAcidPools();

    // Assi e griglia
    // this.scene.add(new THREE.AxesHelper(5));
    this.scene.add(new THREE.GridHelper(20, 20));

    // Input
    this.setupKeyboardMovement();
    this.setupMouseLook();
    this.setupPointerSelection();

    // Resize
    window.addEventListener('resize', this.onResize);
  }

  // resize handler separato per rimozione pulita
  private onResize = () => {
    const aspect = this.rendererContainer.nativeElement.clientWidth /
      this.rendererContainer.nativeElement.clientHeight;
    const frustumSize = 20;
    this.camera.left = (frustumSize * aspect) / -2;
    this.camera.right = (frustumSize * aspect) / 2;
    this.camera.top = frustumSize / 2;
    this.camera.bottom = frustumSize / -2;
    this.camera.updateProjectionMatrix();
    this.renderer.setSize(
      this.rendererContainer.nativeElement.clientWidth,
      this.rendererContainer.nativeElement.clientHeight
    );
  };

  private onKeyDown = (e: KeyboardEvent) => {
    switch (e.key.toLowerCase()) {
      case 'w':
        this.movementState.forward = true;
        break;
      case 's':
        this.movementState.backward = true;
        break;
      case 'a':
        this.movementState.left = true;  // strafe left
        break;
      case 'd':
        this.movementState.right = true; // strafe right
        break;
      case 'q':
        this.movementState.up = true;    // sale in altezza
        break;
      case 'e':
        this.movementState.down = true;  // scende in altezza
        break;
    }
  };

  private onKeyUp = (e: KeyboardEvent) => {
    switch (e.key.toLowerCase()) {
      case 'w':
        this.movementState.forward = false;
        break;
      case 's':
        this.movementState.backward = false;
        break;
      case 'a':
        this.movementState.left = false;
        break;
      case 'd':
        this.movementState.right = false;
        break;
      case 'q':
        this.movementState.up = false;
        break;
      case 'e':
        this.movementState.down = false;
        break;
    }
  };

  private setupKeyboardMovement() {
    window.addEventListener('keydown', this.onKeyDown);
    window.addEventListener('keyup', this.onKeyUp);
  }

  private setupMouseLook() {
    this.renderer.domElement.addEventListener('mousedown', this.onMouseDown);
    this.renderer.domElement.addEventListener('mouseup', this.onMouseUp);
    this.renderer.domElement.addEventListener('mousemove', this.onMouseMove);
    this.renderer.domElement.addEventListener('dragstart', this.preventDrag);
  }

  private setupPointerSelection() {
    this.renderer.domElement.addEventListener('click', this.onClick);
  }

  private preventDrag = (e: Event) => e.preventDefault();

  // ---- DRAG-BASED MOUSE LOOK ----

  private onMouseDown = (e: MouseEvent) => {
    if (e.button !== 0 || this.isAnimatingCamera) return; // solo sinistro e non durante animazione
    this.isDragging = true;
    this.prevMouse = { x: e.clientX, y: e.clientY };
  };

  private onMouseUp = (e: MouseEvent) => {
    if (e.button !== 0) return;
    this.isDragging = false;
    this.prevMouse = null;
  };

  private onMouseMove = (e: MouseEvent) => {
    if (!this.isDragging || !this.prevMouse || this.isAnimatingCamera) return;

    const deltaX = e.clientX - this.prevMouse.x;
    const deltaY = e.clientY - this.prevMouse.y;

    // Update yaw (horizontal rotation) and pitch (vertical rotation)
    this.yaw -= deltaX * this.yawSpeed;
    this.pitch -= deltaY * this.pitchSpeed;

    // Clamp pitch to prevent over-rotation
    this.pitch = Math.max(-this.pitchLimit, Math.min(this.pitchLimit, this.pitch));

    // Apply rotation to head pivot (which contains the camera)
    this.updateCameraRotation();

    this.prevMouse = { x: e.clientX, y: e.clientY };
  };

  private updateCameraRotation() {
    // Apply yaw rotation to the robot pivot (horizontal rotation around Y axis)
    this.robotPivot.rotation.y = this.yaw;

    // Apply pitch rotation to the head pivot (vertical rotation around X axis)
    this.headPivot.rotation.x = this.pitch;
  }

  // ---- POINT-AND-CLICK SELECTION ----

  private onClick = (e: MouseEvent) => {
    
    const rect = this.renderer.domElement.getBoundingClientRect();
    this.pointer.x = ((e.clientX - rect.left) / rect.width) * 2 - 1;
    this.pointer.y = -((e.clientY - rect.top) / rect.height) * 2 + 1;

    this.raycaster.setFromCamera(this.pointer, this.camera);

    // Filter out non-selectable objects from raycast targets
    const selectableObjects = this.scene.children.filter(child =>
      child !== this.highlightOutline &&
      !(child instanceof THREE.Sprite) &&
      !(child instanceof THREE.GridHelper) &&
      !(child instanceof THREE.AxesHelper) &&
      !(child instanceof THREE.DirectionalLight) &&
      !(child instanceof THREE.AmbientLight)
    );
    
    const intersects = this.raycaster.intersectObjects(selectableObjects, true);

    
    if (intersects.length > 0) {
      const hit = intersects[0].object;

      // Deselect precedente
      this.clearHighlight();

      // Selezione corrente
      this.selectedThingMesh = hit;

      // Evidenzia con outline wireframe box
      const box = new THREE.Box3().setFromObject(hit);
      const size = new THREE.Vector3();
      const center = new THREE.Vector3();
      box.getSize(size);
      box.getCenter(center);

      // Store selected object position for rotation pivot
      this.selectedObjectPosition = center.clone();

      // Initialize orbit camera from current position
      // this.initializeOrbitCamera();

      const geometry = new THREE.BoxGeometry(
        Math.max(size.x, 0.001) * 1.05,
        Math.max(size.y, 0.001) * 1.05,
        Math.max(size.z, 0.001) * 1.05
      );
      const edges = new THREE.EdgesGeometry(geometry);
      const outline = new THREE.LineSegments(
        edges,
        new THREE.LineBasicMaterial({ color: 0xff0000 })
      );
      outline.position.copy(center);
      this.highlightOutline = outline;
      this.scene.add(outline);

      // Mostra nome come sprite sopra (se presente)
      if (hit.name) {
        const sprite = ThreeComponent.createTextSprite(hit.name);
        sprite.position.copy(center);
        sprite.position.y += size.y + 0.2;
        this.scene.add(sprite);
        setTimeout(() => this.scene.remove(sprite), 2500);
      }

      console.log('Clicked on thing:', hit.name || hit.uuid);

      // Don't animate camera when we're now rotating around the object
      // The rotation system will handle positioning automatically
    } else {
      this.clearHighlight();
      this.selectedThingMesh = null;
      this.selectedObjectPosition = null;
    }
  };

  private clearHighlight() {
    if (this.highlightOutline) {
      this.scene.remove(this.highlightOutline);
      this.highlightOutline.geometry.dispose();
      (this.highlightOutline.material as THREE.Material).dispose();
      this.highlightOutline = null;
    }
    this.selectedObjectPosition = null;
  }





private applyKeyboardMovement(delta: number) {
  const moveSpeed = 5 * delta;

  // Ottieni direzione "forward" della camera, proiettata sul piano XZ (y = 0)
  const camDir = new THREE.Vector3();
  this.camera.getWorldDirection(camDir); // include pitch
  camDir.y = 0;
  camDir.normalize();

  // Direzione right come ortogonale in XZ
  const camRight = new THREE.Vector3();
  camRight.crossVectors(camDir, new THREE.Vector3(0, 1, 0)).normalize();

  if (this.movementState.forward) {
    this.robotPivot.position.addScaledVector(camDir, moveSpeed);
  }
  if (this.movementState.backward) {
    this.robotPivot.position.addScaledVector(camDir, -moveSpeed);
  }
  if (this.movementState.left) {
    this.robotPivot.position.addScaledVector(camRight, -moveSpeed);
  }
  if (this.movementState.right) {
    this.robotPivot.position.addScaledVector(camRight, moveSpeed);
  }
  if (this.movementState.up) {
    this.robotPivot.position.y += moveSpeed;
  }
  if (this.movementState.down) {
    this.robotPivot.position.y -= moveSpeed;
  }
}

  private animate = () => {
    this.animationId = requestAnimationFrame(this.animate);
    const delta = this.clock.getDelta(); // delta reale
    const elapsedTime = this.clock.getElapsedTime();

    // Handle camera animation
    this.updateCameraAnimation();

    // Only apply keyboard movement if not animating camera
    if (!this.isAnimatingCamera) {
      this.applyKeyboardMovement(delta);
    }

    // Animate acid bubbles
    this.animateAcidBubbles(elapsedTime);

    this.renderer.render(this.scene, this.camera);
  };

  private updateCameraAnimation() {
    return;
    // if (!this.isAnimatingCamera) return;

    // const currentTime = performance.now();
    // const elapsed = currentTime - this.animationStartTime;
    // const progress = Math.min(elapsed / this.animationDuration, 1);

    // // Use easing function for smooth animation (ease-in-out)
    // const easedProgress = this.easeInOutCubic(progress);

    // // Only animate position if we're not rotating around a selected object
    // if (!this.selectedObjectPosition) {
    //   // Interpolate position
    //   this.robotPivot.position.lerpVectors(
    //     this.startCameraPosition,
    //     this.targetCameraPosition,
    //     easedProgress
    //   );

    //   // Only interpolate rotation if target rotation is different from start rotation
    //   const rotationChanged =
    //     Math.abs(this.startCameraRotation.y - this.targetCameraRotation.y) > 0.001 ||
    //     Math.abs(this.startCameraRotation.x - this.targetCameraRotation.x) > 0.001;

    //   if (rotationChanged) {
    //     this.currentYaw = this.lerp(
    //       this.startCameraRotation.y,
    //       this.targetCameraRotation.y,
    //       easedProgress
    //     );
    //     this.currentPitch = this.lerp(
    //       this.startCameraRotation.x,
    //       this.targetCameraRotation.x,
    //       easedProgress
    //     );

    //     // Update camera rotation
    //     this.updateCameraRotation();
    //   }
    // }

    // // Check if animation is complete
    // if (progress >= 1) {
    //   this.isAnimatingCamera = false;
    //   // Only update target values if we're not rotating around a selected object
    //   if (!this.selectedObjectPosition) {
    //     this.yaw = this.currentYaw;
    //     this.pitch = this.currentPitch;
    //   }
    // }
  }

  // private easeInOutCubic(t: number): number {
  //   return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
  // }

  private lerp(start: number, end: number, t: number): number {
    return start + (end - start) * t;
  }

  private animateAcidBubbles(elapsedTime: number) {
    // Find all bubble meshes and animate them
    this.scene.traverse((child) => {
      if (child instanceof THREE.Mesh &&
          child.geometry instanceof THREE.SphereGeometry &&
          child.userData['animationSpeed'] !== undefined) {

        // Animate vertical bobbing motion
        const animationData = child.userData;
        const bobHeight = 0.05;
        const yOffset = Math.sin(elapsedTime * animationData['animationSpeed'] + animationData['animationOffset']) * bobHeight;
        child.position.y = animationData['originalY'] + yOffset;

        // Animate opacity for breathing effect
        if (child.material instanceof THREE.MeshBasicMaterial) {
          const opacityVariation = 0.2;
          const baseOpacity = 0.6;
          child.material.opacity = baseOpacity + Math.sin(elapsedTime * 2 + animationData['animationOffset']) * opacityVariation;
        }
      }
    });
  }

  private addAcidPools() {
    // Create several neon green acid pools scattered around the scene
    const acidPositions = [
      { x: -8, z: -8, size: 2 },
      { x: 8, z: 8, size: 1.5 },
      { x: -6, z: 6, size: 1.8 },
      { x: 10, z: -5, size: 1.2 },
      { x: -3, z: -10, size: 2.2 }
    ];

    acidPositions.forEach((pos) => {
      // Create acid pool geometry
      const acidGeometry = new THREE.CircleGeometry(pos.size, 32);
      const acidMaterial = new THREE.MeshPhysicalMaterial({
        color: 0x00ff00, // Bright neon green
        emissive: 0x004400, // Glowing effect
        emissiveIntensity: 0.3,
        metalness: 0.0,
        roughness: 0.1,
        transparent: true,
        opacity: 0.8,
        clearcoat: 1.0,
        clearcoatRoughness: 0.0
      });

      const acidPool = new THREE.Mesh(acidGeometry, acidMaterial);
      acidPool.rotation.x = -Math.PI / 2; // Horizontal
      acidPool.position.set(pos.x, 0.005, pos.z); // Slightly above ground
      this.scene.add(acidPool);

      // Add glowing ring around each pool
      const ringGeometry = new THREE.RingGeometry(pos.size * 0.9, pos.size * 1.1, 32);
      const ringMaterial = new THREE.MeshBasicMaterial({
        color: 0x00ff00,
        transparent: true,
        opacity: 0.3,
        side: THREE.DoubleSide
      });
      const ring = new THREE.Mesh(ringGeometry, ringMaterial);
      ring.rotation.x = -Math.PI / 2;
      ring.position.set(pos.x, 0.01, pos.z);
      this.scene.add(ring);

      // Add point light for each acid pool
      const acidLight = new THREE.PointLight(0x00ff00, 0.5, pos.size * 3);
      acidLight.position.set(pos.x, 0.5, pos.z);
      this.scene.add(acidLight);

      // Add animated bubbling effect
      this.createBubblingEffect(pos.x, pos.z, pos.size);
    });
  }

  private createBubblingEffect(x: number, z: number, poolSize: number) {
    // Create small bubble particles
    const bubbleCount = Math.floor(poolSize * 3);
    for (let i = 0; i < bubbleCount; i++) {
      const bubbleGeometry = new THREE.SphereGeometry(0.02 + Math.random() * 0.03, 8, 6);
      const bubbleMaterial = new THREE.MeshBasicMaterial({
        color: 0x00ff00,
        transparent: true,
        opacity: 0.6
      });
      const bubble = new THREE.Mesh(bubbleGeometry, bubbleMaterial);

      // Random position within the pool
      const angle = Math.random() * Math.PI * 2;
      const radius = Math.random() * poolSize * 0.8;
      bubble.position.set(
        x + Math.cos(angle) * radius,
        0.02,
        z + Math.sin(angle) * radius
      );

      // Store animation data
      bubble.userData = {
        originalY: bubble.position.y,
        animationSpeed: 0.5 + Math.random() * 1.0,
        animationOffset: Math.random() * Math.PI * 2
      };

      this.scene.add(bubble);
    }
  }

  private loadJsonScene(data: { things: ThingData[] }) {
    data.things.forEach((obj) => {
      let thing: AbstractThing | null = null;
      switch (obj.type.toLowerCase()) {
        case 'box':
          thing = new BoxThing(obj);
          break;
        case 'sphere':
          thing = new SphereThing(obj);
          break;
        case 'triangle':
          thing = new TriangleThing(obj);
          break;
        case 'rectangle':
          thing = new RectangleThing(obj);
          break;
        default:
          console.warn(`Unknown thing type: ${obj.type}`);
      }
      if (thing) {
        const mesh = thing.getObject();
        mesh.position.set(obj.pos[0], obj.pos[1], obj.pos[2]);
        mesh.name = obj.name;
        mesh.userData['thingData'] = obj;
        mesh.castShadow = true; // Enable shadow casting for objects
        this.scene.add(mesh);
      }
    });
  }

  static createTextSprite(text: string): THREE.Sprite {
    const canvas = document.createElement('canvas');
    canvas.width = 512;
    canvas.height = 128;
    const context = canvas.getContext('2d')!;
    context.font = '48px Arial';
    context.fillStyle = 'black';
    context.fillText(text, 10, 64);
    const texture = new THREE.CanvasTexture(canvas);
    texture.needsUpdate = true;
    const spriteMaterial = new THREE.SpriteMaterial({ map: texture });
    const sprite = new THREE.Sprite(spriteMaterial);
    sprite.scale.set(3, 1, 1);
    return sprite;
  }
}
